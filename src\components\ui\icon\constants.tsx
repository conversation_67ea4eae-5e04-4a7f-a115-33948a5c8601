import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ogo,
  GeminiLogo,
  Aws<PERSON>ogo,
  Azure<PERSON>ogo,
  AnthropicLogo,
  GroqLogo,
  FireworksLogo,
  DeepseekLogo,
  CohereLogo,
  OllamaLogo,
  XaiLogo,
  AgnoIcon,
  UserIcon,
  AgentIcon,
  SheetIcon,
  NextjsTag,
  ShadcnTag,
  TailwindTag,
  AgnoTag,
  ReasoningIcon,
  ReferencesIcon
} from './custom-icons'
import { IconTypeMap } from './types'
import {
  RefreshCw,
  Edit,
  Save,
  X,
  ArrowDown,
  SendIcon,
  Download,
  HammerIcon,
  Check,
  ChevronDown,
  ChevronUp,
  Trash
} from 'lucide-react'

import { PlusIcon } from '@radix-ui/react-icons'

export const ICONS: IconTypeMap = {
  'open-ai': OpenAILogo,
  mistral: MistralLogo,
  gemini: GeminiLogo,
  aws: AwsLogo,
  azure: AzureLogo,
  anthropic: AnthropicLogo,
  groq: GroqLogo,
  fireworks: FireworksLogo,
  deepseek: DeepseekLogo,
  cohere: <PERSON>here<PERSON><PERSON>,
  ollama: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  xai: <PERSON>ai<PERSON><PERSON>,
  agno: Agno<PERSON><PERSON>,
  user: UserI<PERSON>,
  agent: AgentIcon,
  sheet: SheetIcon,
  nextjs: NextjsTag,
  shadcn: ShadcnTag,
  tailwind: TailwindTag,
  reasoning: ReasoningIcon,
  'agno-tag': AgnoTag,
  refresh: RefreshCw,
  edit: Edit,
  save: Save,
  x: X,
  'arrow-down': ArrowDown,
  send: SendIcon,
  download: Download,
  hammer: HammerIcon,
  check: Check,
  'chevron-down': ChevronDown,
  'chevron-up': ChevronUp,
  'plus-icon': PlusIcon,
  references: ReferencesIcon,
  trash: Trash
}
