const AgentThinkingLoader = () => (
  <div className="flex items-center gap-1">
    <div className="size-1.5 animate-bounce rounded-full bg-primary/20 [animation-delay:-0.3s] [animation-duration:0.70s]" />
    <div className="size-1.5 animate-bounce rounded-full bg-primary/20 [animation-delay:-0.10s] [animation-duration:0.70s]" />
    <div className="size-1.5 animate-bounce rounded-full bg-primary/20 [animation-duration:0.70s]" />
  </div>
)

export default AgentThinkingLoader
